nohup: ignoring input
INFO:     Will watch for changes in these directories: ['/home/<USER>/Desktop/WebReview_DS_API_24Jun']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [60620] using StatReload
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/main.py", line 4, in <module>
    from app.database import init_db
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/database.py", line 15, in <module>
    engine = create_engine(
        settings.DATABASE_URL,
    ...<6 lines>...
        connect_args=connect_args
    )
  File "<string>", line 2, in create_engine
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/sqlalchemy/util/deprecations.py", line 281, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/sqlalchemy/engine/create.py", line 602, in create_engine
    dbapi = dbapi_meth(**dbapi_args)
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/sqlalchemy/dialects/mysql/pymysql.py", line 74, in import_dbapi
    return __import__("pymysql")
ModuleNotFoundError: No module named 'pymysql'
WARNING:  StatReload detected changes in 'app/Extractor/services/entity_extractor_orchestrator.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/main.py", line 4, in <module>
    from app.database import init_db
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/database.py", line 15, in <module>
    engine = create_engine(
        settings.DATABASE_URL,
    ...<6 lines>...
        connect_args=connect_args
    )
  File "<string>", line 2, in create_engine
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/sqlalchemy/util/deprecations.py", line 281, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/sqlalchemy/engine/create.py", line 602, in create_engine
    dbapi = dbapi_meth(**dbapi_args)
  File "/home/<USER>/.linuxbrew/lib/python3.13/site-packages/sqlalchemy/dialects/mysql/pymysql.py", line 74, in import_dbapi
    return __import__("pymysql")
ModuleNotFoundError: No module named 'pymysql'
