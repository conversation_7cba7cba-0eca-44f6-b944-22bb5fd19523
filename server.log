nohup: ignoring input
INFO:     Started server process [58468]
INFO:     Waiting for application startup.
2025-07-31 18:21:22,712 - app.main - INFO - Initializing Entity Extraction API
2025-07-31 18:21:23,130 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-07-31 18:21:23,130 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-07-31 18:21:23,130 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,130 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,250 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-07-31 18:21:23,250 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-07-31 18:21:23,250 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,250 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,310 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-07-31 18:21:23,310 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-07-31 18:21:23,310 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,310 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,429 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:23,429 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:23,429 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:21:23,429 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:21:23,429 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,429 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,491 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:21:23,491 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:21:23,491 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,491 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,562 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:21:23,562 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:21:23,562 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,562 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,626 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:21:23,626 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:21:23,626 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,626 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,682 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:21:23,682 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:21:23,682 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,682 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,743 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:21:23,743 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:21:23,743 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,743 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,791 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:21:23,791 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:21:23,792 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:23,792 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:23,861 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:23,861 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:21:23,979 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-07-31 18:21:24,024 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:24,024 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:24,025 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:21:24,025 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-31 18:21:24,025 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:24,025 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:24,071 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:21:24,071 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-31 18:21:24,071 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:24,071 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:24,154 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:21:24,154 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-31 18:21:24,154 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:24,154 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:24,211 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:21:24,211 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-31 18:21:24,211 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:24,211 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:24,272 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:21:24,272 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-31 18:21:24,272 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:24,272 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:24,334 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:21:24,334 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-07-31 18:21:24,334 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:24,334 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:24,399 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:21:24,399 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-07-31 18:21:24,399 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 18:21:24,399 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 18:21:24,451 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:24,451 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:21:24,571 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
[2025-07-31 18:21:33][EntityExtractor][orchestrator_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a",
  "website_url": "https://www.houseofaks.in/",
  "org_id": "default"
}
2025-07-31 18:21:33,870 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:33,870 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:33,871 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:33,871 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:33,872 INFO sqlalchemy.engine.Engine [generated in 0.00016s] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:33.799192', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a",  ... (34 characters truncated) ... 799184", "data": {"scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "website_url": "https://www.houseofaks.in/", "org_id": "default"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:33,872 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:33.799192', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a",  ... (34 characters truncated) ... 799184", "data": {"scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "website_url": "https://www.houseofaks.in/", "org_id": "default"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:33,940 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:33,940 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:21:34,099 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:34,099 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:34,102 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:21:34,102 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:21:34,103 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
2025-07-31 18:21:34,103 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
[2025-07-31 18:21:34][EntityExtractor][orchestrator_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Found existing analysis with ID: 131, status: FAILED
2025-07-31 18:21:34,610 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:34,610 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:34,610 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:34,610 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:34,610 INFO sqlalchemy.engine.Engine [cached since 0.7388s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:34.188490', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 131, status: FAILED", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:34.188482", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:34,610 - sqlalchemy.engine.Engine - INFO - [cached since 0.7388s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:34.188490', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 131, status: FAILED", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:34.188482", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:34,682 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:34,682 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:21:34,840 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 18:21:34,840 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-31 18:21:35,087 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:35,087 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:35,087 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:21:35,087 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-07-31 18:21:35,088 INFO sqlalchemy.engine.Engine [cached since 0.9852s ago] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
2025-07-31 18:21:35,088 - sqlalchemy.engine.Engine - INFO - [cached since 0.9852s ago] {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'org_id_1': 'default'}
[2025-07-31 18:21:35][EntityExtractor][orchestrator_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] WARNING: Found existing analysis during create - returning existing ID: 131
2025-07-31 18:21:35,210 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:35,210 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:35,210 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:35,210 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:35,210 INFO sqlalchemy.engine.Engine [cached since 1.339s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:35.156954', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 131", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:35.156947", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:35,210 - sqlalchemy.engine.Engine - INFO - [cached since 1.339s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:35.156954', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 131", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:35.156947", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:35,285 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:35,285 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:21:35,411 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 18:21:35,411 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-31 18:21:35][EntityExtractor][131][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Updated logger with analysis ID: 131
2025-07-31 18:21:35,600 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:35,600 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:35,600 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:35,600 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:35,600 INFO sqlalchemy.engine.Engine [cached since 1.729s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:35.535657', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 131", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:35.535649", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:35,600 - sqlalchemy.engine.Engine - INFO - [cached since 1.729s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:35.535657', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 131", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:35.535649", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:35,690 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:35,690 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:21:35,869 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:35,869 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:35,870 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:21:35,870 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:21:35,870 INFO sqlalchemy.engine.Engine [generated in 0.00014s] {'pk_1': 131}
2025-07-31 18:21:35,870 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] {'pk_1': 131}
2025-07-31 18:21:35,953 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-07-31 18:21:35,953 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-07-31 18:21:35,953 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-07-31T18:21:35.809439', 'entity_extraction_analysis_id': 131}
2025-07-31 18:21:35,953 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-07-31T18:21:35.809439', 'entity_extraction_analysis_id': 131}
2025-07-31 18:21:36,020 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:36,020 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:36][EntityExtractor][131][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Updated analysis 131 status to IN_PROGRESS
2025-07-31 18:21:36,190 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:36,190 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:36,190 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:36,190 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:36,190 INFO sqlalchemy.engine.Engine [cached since 2.319s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:36.143484', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 131 status to IN_PROGRESS", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:36.143473", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:36,190 - sqlalchemy.engine.Engine - INFO - [cached since 2.319s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:36.143484', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 131 status to IN_PROGRESS", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:36.143473", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:36,241 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:36,241 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:36][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Retrieving policy URLs with reachability status
2025-07-31 18:21:36,411 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:36,411 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:36,411 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:36,411 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:36,411 INFO sqlalchemy.engine.Engine [cached since 2.54s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:36.350384', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:36.350377", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:36,411 - sqlalchemy.engine.Engine - INFO - [cached since 2.54s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:36.350384', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:36.350377", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:36,471 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:36,471 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:37][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Error getting latest scrape_request_ref_id
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s \n LIMIT %(param_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py\", line 370, in _get_latest_scrape_request_ref_id\n    current_record = session.exec(current_query).first()\n                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s \n LIMIT %(param_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py", line 370, in _get_latest_scrape_request_ref_id
    current_record = session.exec(current_query).first()
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")
[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id 
FROM mcc_url_classification_gemini 
WHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s 
 LIMIT %(param_1)s]
[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a:
2025-07-31 18:21:37,715 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:37,715 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:37,716 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:37,716 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:37,716 INFO sqlalchemy.engine.Engine [cached since 3.844s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:37.652659', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error getting latest scrape_request_ref_id", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timesta ... (9625 characters truncated) ... e_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\', \'param_1\': 1}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:37,716 - sqlalchemy.engine.Engine - INFO - [cached since 3.844s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:37.652659', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error getting latest scrape_request_ref_id", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timesta ... (9625 characters truncated) ... e_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\', \'param_1\': 1}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:37,838 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:37,838 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:38][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Error retrieving MCC policy URLs
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py\", line 326, in _get_mcc_policy_urls_latest\n    mcc_records = session.exec(mcc_query).all()\n                  ~~~~~~~~~~~~^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py", line 326, in _get_mcc_policy_urls_latest
    mcc_records = session.exec(mcc_query).all()
                  ~~~~~~~~~~~~^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")
[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id 
FROM mcc_url_classification_gemini 
WHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]
[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a:
2025-07-31 18:21:38,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:38,231 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:38,232 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:38,232 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:38,232 INFO sqlalchemy.engine.Engine [cached since 4.361s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:38.174050', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error retrieving MCC policy URLs", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025 ... (9517 characters truncated) ... meters: {\'scrape_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\'}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:38,232 - sqlalchemy.engine.Engine - INFO - [cached since 4.361s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:38.174050', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error retrieving MCC policy URLs", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025 ... (9517 characters truncated) ... meters: {\'scrape_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\'}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:38,321 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:38,321 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:38][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-07-31 18:21:38,582 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:38,582 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:38,582 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:38,582 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:38,582 INFO sqlalchemy.engine.Engine [cached since 4.711s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:38.493330', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:38.493322", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:38,582 - sqlalchemy.engine.Engine - INFO - [cached since 4.711s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:38.493330', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:38.493322", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:38,688 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:38,688 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:39][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Error getting latest scrape_request_ref_id
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s \n LIMIT %(param_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py\", line 370, in _get_latest_scrape_request_ref_id\n    current_record = session.exec(current_query).first()\n                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s \n LIMIT %(param_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py", line 370, in _get_latest_scrape_request_ref_id
    current_record = session.exec(current_query).first()
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")
[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id 
FROM mcc_url_classification_gemini 
WHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s 
 LIMIT %(param_1)s]
[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a:
2025-07-31 18:21:39,262 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:39,262 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:39,262 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:39,262 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:39,262 INFO sqlalchemy.engine.Engine [cached since 5.391s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:39.157473', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error getting latest scrape_request_ref_id", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timesta ... (9625 characters truncated) ... e_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\', \'param_1\': 1}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:39,262 - sqlalchemy.engine.Engine - INFO - [cached since 5.391s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:39.157473', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error getting latest scrape_request_ref_id", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timesta ... (9625 characters truncated) ... e_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\', \'param_1\': 1}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:39,432 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:39,432 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:39][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Error retrieving policy analysis data
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'\")\n[SQL: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_conditions_url, policy_analysis_new_gemini.shipping_policy_url, policy_analysis_new_gemini.returns_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_conditions_text, policy_analysis_new_gemini.shipping_policy_text, policy_analysis_new_gemini.returns_policy_text, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id \nFROM policy_analysis_new_gemini \nWHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py\", line 422, in _get_policy_analysis_data\n    policy_record = session.exec(policy_query).first()\n                    ~~~~~~~~~~~~^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'\")\n[SQL: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_conditions_url, policy_analysis_new_gemini.shipping_policy_url, policy_analysis_new_gemini.returns_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_conditions_text, policy_analysis_new_gemini.shipping_policy_text, policy_analysis_new_gemini.returns_policy_text, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id \nFROM policy_analysis_new_gemini \nWHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py", line 422, in _get_policy_analysis_data
    policy_record = session.exec(policy_query).first()
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'")
[SQL: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_conditions_url, policy_analysis_new_gemini.shipping_policy_url, policy_analysis_new_gemini.returns_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_conditions_text, policy_analysis_new_gemini.shipping_policy_text, policy_analysis_new_gemini.returns_policy_text, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]
[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a:
2025-07-31 18:21:39,872 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:39,872 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:39,872 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:39,872 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:39,872 INFO sqlalchemy.engine.Engine [cached since 6.001s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:39.818014', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error retrieving policy analysis data", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp":  ... (10819 characters truncated) ... meters: {\'scrape_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\'}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:39,872 - sqlalchemy.engine.Engine - INFO - [cached since 6.001s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:39.818014', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error retrieving policy analysis data", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp":  ... (10819 characters truncated) ... meters: {\'scrape_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\'}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:39,953 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:39,953 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:40][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: No policy URLs found for scrape_request_ref_id, creating fallback entry for website: https://www.houseofaks.in/
2025-07-31 18:21:40,132 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:40,132 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:40,132 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:40,132 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:40,132 INFO sqlalchemy.engine.Engine [cached since 6.261s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:40.070466', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "No policy URLs found for scrape_request_ref_id, creating fallback entry for website: https://www.houseofaks.in/", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:40.070458", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:40,132 - sqlalchemy.engine.Engine - INFO - [cached since 6.261s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:40.070466', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "No policy URLs found for scrape_request_ref_id, creating fallback entry for website: https://www.houseofaks.in/", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:40.070458", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:40,202 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:40,202 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:40][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Total policy URLs retrieved: 1
2025-07-31 18:21:40,471 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:40,471 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:40,471 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:40,471 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:40,472 INFO sqlalchemy.engine.Engine [cached since 6.6s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:40.378133', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 1", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:40.378125", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:40,472 - sqlalchemy.engine.Engine - INFO - [cached since 6.6s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:40.378133', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 1", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:40.378125", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:40,540 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:40,540 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:40][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Error getting latest scrape_request_ref_id
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s \n LIMIT %(param_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py\", line 370, in _get_latest_scrape_request_ref_id\n    current_record = session.exec(current_query).first()\n                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'mcc_url_classification_gemini.classification' in 'field list'\")\n[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id \nFROM mcc_url_classification_gemini \nWHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s \n LIMIT %(param_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py", line 370, in _get_latest_scrape_request_ref_id
    current_record = session.exec(current_query).first()
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'mcc_url_classification_gemini.classification' in 'field list'")
[SQL: SELECT mcc_url_classification_gemini.id, mcc_url_classification_gemini.scrape_request_ref_id, mcc_url_classification_gemini.url, mcc_url_classification_gemini.classification, mcc_url_classification_gemini.created_at, mcc_url_classification_gemini.org_id 
FROM mcc_url_classification_gemini 
WHERE mcc_url_classification_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s 
 LIMIT %(param_1)s]
[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a:
2025-07-31 18:21:40,909 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:40,909 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:40,909 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:40,909 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:40,910 INFO sqlalchemy.engine.Engine [cached since 7.038s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:40.865513', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error getting latest scrape_request_ref_id", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timesta ... (9625 characters truncated) ... e_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\', \'param_1\': 1}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:40,910 - sqlalchemy.engine.Engine - INFO - [cached since 7.038s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:40.865513', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error getting latest scrape_request_ref_id", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timesta ... (9625 characters truncated) ... e_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\', \'param_1\': 1}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:41,002 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:41,002 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:41][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Error retrieving policy analysis data
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'\")\n[SQL: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_conditions_url, policy_analysis_new_gemini.shipping_policy_url, policy_analysis_new_gemini.returns_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_conditions_text, policy_analysis_new_gemini.shipping_policy_text, policy_analysis_new_gemini.returns_policy_text, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id \nFROM policy_analysis_new_gemini \nWHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py\", line 422, in _get_policy_analysis_data\n    policy_record = session.exec(policy_query).first()\n                    ~~~~~~~~~~~~^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'\")\n[SQL: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_conditions_url, policy_analysis_new_gemini.shipping_policy_url, policy_analysis_new_gemini.returns_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_conditions_text, policy_analysis_new_gemini.shipping_policy_text, policy_analysis_new_gemini.returns_policy_text, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id \nFROM policy_analysis_new_gemini \nWHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]\n[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/url_data_retrieval.py", line 422, in _get_policy_analysis_data
    policy_record = session.exec(policy_query).first()
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'policy_analysis_new_gemini.terms_conditions_url' in 'field list'")
[SQL: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_conditions_url, policy_analysis_new_gemini.shipping_policy_url, policy_analysis_new_gemini.returns_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_conditions_text, policy_analysis_new_gemini.shipping_policy_text, policy_analysis_new_gemini.returns_policy_text, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s]
[parameters: {'scrape_request_ref_id_1': '8c263e07-04a6-4acc-8a81-17bf2e39636a'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a:
2025-07-31 18:21:41,417 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:41,417 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:41,417 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:41,417 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:41,417 INFO sqlalchemy.engine.Engine [cached since 7.546s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:41.320234', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error retrieving policy analysis data", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp":  ... (10819 characters truncated) ... meters: {\'scrape_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\'}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:41,417 - sqlalchemy.engine.Engine - INFO - [cached since 7.546s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:41.320234', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error retrieving policy analysis data", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp":  ... (10819 characters truncated) ... meters: {\'scrape_request_ref_id_1\': \'8c263e07-04a6-4acc-8a81-17bf2e39636a\'}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:41,580 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:41,580 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:41][EntityExtractor][131][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Filtered policy URLs keys: ['home_page']
2025-07-31 18:21:41,797 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:41,797 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:41,798 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:41,798 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:41,798 INFO sqlalchemy.engine.Engine [cached since 7.926s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:41.711072', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'home_page\']", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:41.711062", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:41,798 - sqlalchemy.engine.Engine - INFO - [cached since 7.926s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:41.711072', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'home_page\']", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:41.711062", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:41,866 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:41,866 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:42][EntityExtractor][131][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Key privacy_policy missing in filtered_policy_urls
2025-07-31 18:21:42,111 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:42,111 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:42,111 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:42,111 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:42,111 INFO sqlalchemy.engine.Engine [cached since 8.24s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:42.044367', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Key privacy_policy missing in filtered_policy_urls", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.044360", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:42,111 - sqlalchemy.engine.Engine - INFO - [cached since 8.24s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:42.044367', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Key privacy_policy missing in filtered_policy_urls", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.044360", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:42,170 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:42,170 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:42][EntityExtractor][131][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Key terms_and_condition missing in filtered_policy_urls
2025-07-31 18:21:42,375 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:42,375 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:42,376 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:42,376 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:42,376 INFO sqlalchemy.engine.Engine [cached since 8.504s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:42.320037', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Key terms_and_condition missing in filtered_policy_urls", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.320030", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:42,376 - sqlalchemy.engine.Engine - INFO - [cached since 8.504s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:21:42.320037', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Key terms_and_condition missing in filtered_policy_urls", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.320030", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:42,435 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:42,435 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:42][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Checking Gemini reachability for 1 URLs
2025-07-31 18:21:42,601 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:42,601 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:42,601 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:42,601 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:42,601 INFO sqlalchemy.engine.Engine [cached since 8.73s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:42.540253', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 1 URLs", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.540246", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:42,601 - sqlalchemy.engine.Engine - INFO - [cached since 8.73s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:42.540253', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 1 URLs", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.540246", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:42,694 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:42,694 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-31 18:21:42][EntityExtractor][url_retrieval_8c263e07-04a6-4acc-8a81-17bf2e39636a][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Gemini reachability check completed: 1 reachable, 0 unreachable
2025-07-31 18:21:43,016 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:21:43,016 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:21:43,017 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:43,017 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:21:43,017 INFO sqlalchemy.engine.Engine [cached since 9.145s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:42.895380', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 1 reachable, 0 unreachable", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.895373", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:43,017 - sqlalchemy.engine.Engine - INFO - [cached since 9.145s ago] {'analysis_id': 0, 'timestamp': '2025-07-31T18:21:42.895380', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 1 reachable, 0 unreachable", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:21:42.895373", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:21:43,140 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:21:43,140 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:21:48,310 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-31 18:22:09,122 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-31 18:22:09,134 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-07-31 18:21:48][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5147,
  "context": {
    "task_type": "legacy"
  }
}
[2025-07-31 18:21:48][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-07-31 18:22:09][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=591 candidates_tokens_details=None prompt_token_count=1201 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1201
)] thoughts_token_count=2001 tool_use_prompt_token_count=314 tool_use_prompt_tokens_details=None total_token_count=4107 traffic_type=None
[2025-07-31 18:22:09][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 1738,
  "finish_reason": "STOP"
}
API response logged to: api_logs/gemini_20250731_182211_800b4ed7.json
[2025-07-31 18:22:11][EntityExtractor][131][8c263e07-04a6-4acc-8a81-17bf2e39636a] ERROR: Entity extraction failed: APIResponseLogger.log_gemini_response() missing 1 required positional argument: 'prompt'
2025-07-31 18:22:11,209 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:22:11,209 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:22:11,209 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:22:11,209 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:22:11,210 INFO sqlalchemy.engine.Engine [cached since 37.34s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:22:11.137073', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Entity extraction failed: APIResponseLogger.log_gemini_response() missing 1 required positional argument: \'prompt\'", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:22:11.137067", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:22:11,210 - sqlalchemy.engine.Engine - INFO - [cached since 37.34s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:22:11.137073', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Entity extraction failed: APIResponseLogger.log_gemini_response() missing 1 required positional argument: \'prompt\'", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:22:11.137067", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:22:11,295 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:22:11,295 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:22:11,440 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:22:11,440 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:22:11,441 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-07-31 18:22:11,441 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-07-31 18:22:11,441 INFO sqlalchemy.engine.Engine [generated in 0.00024s] {'scrape_request_ref_id': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'website_url': 'https://www.houseofaks.in/', 'processing_status': 'FAILED', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-07-31T18:22:11.385143', 'started_at': '2025-07-31T18:22:11.385130', 'completed_at': None, 'error_message': "APIResponseLogger.log_gemini_response() missing 1 required positional argument: 'prompt'", 'org_id': 'default'}
2025-07-31 18:22:11,441 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] {'scrape_request_ref_id': '8c263e07-04a6-4acc-8a81-17bf2e39636a', 'website_url': 'https://www.houseofaks.in/', 'processing_status': 'FAILED', 'legal_name': None, 'business_email': None, 'support_email': None, 'business_contact_numbers': None, 'business_location': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-07-31T18:22:11.385143', 'started_at': '2025-07-31T18:22:11.385130', 'completed_at': None, 'error_message': "APIResponseLogger.log_gemini_response() missing 1 required positional argument: 'prompt'", 'org_id': 'default'}
2025-07-31 18:22:11,501 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:22:11,501 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:22:11,682 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:22:11,682 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:22:11,683 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:22:11,683 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-07-31 18:22:11,683 INFO sqlalchemy.engine.Engine [generated in 0.00014s] {'pk_1': 132}
2025-07-31 18:22:11,683 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] {'pk_1': 132}
[2025-07-31 18:22:11][EntityExtractor][131][8c263e07-04a6-4acc-8a81-17bf2e39636a] INFO: Stored failed entity extraction analysis with ID: 132
2025-07-31 18:22:11,803 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 18:22:11,803 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 18:22:11,803 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:22:11,803 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-07-31 18:22:11,804 INFO sqlalchemy.engine.Engine [cached since 37.93s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:22:11.746117', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored failed entity extraction analysis with ID: 132", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:22:11.746109", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:22:11,804 - sqlalchemy.engine.Engine - INFO - [cached since 37.93s ago] {'analysis_id': 131, 'timestamp': '2025-07-31T18:22:11.746117', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored failed entity extraction analysis with ID: 132", "scrape_request_ref_id": "8c263e07-04a6-4acc-8a81-17bf2e39636a", "timestamp": "2025-07-31T18:22:11.746109", "data": {}}', 'response': '', 'org_id': 'default'}
2025-07-31 18:22:11,860 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 18:22:11,860 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 18:22:11,964 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 18:22:11,964 - sqlalchemy.engine.Engine - INFO - ROLLBACK
INFO:     127.0.0.1:40686 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
